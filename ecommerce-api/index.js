const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
require('dotenv').config();
const bcrypt = require('bcrypt');
const { createClient } = require('@supabase/supabase-js');

const app = express();
const port = 3001;

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

app.use(cors());
app.use(express.json());

// Admin Login
app.post('/login', async (req, res) => {
  const { username, password } = req.body;

  const { data, error } = await supabase
    .from('admin')
    .select('*')
    .eq('username', username)
    .single();

  if (error || !data) return res.status(401).send('User not found');
  
  const match = await bcrypt.compare(password, data.password);
  if (!match) return res.status(401).send('Wrong password');

  const token = jwt.sign({ username }, process.env.JWT_SECRET, { expiresIn: '1h' });
  res.json({ token });
});

// Get Products
//app.get('/products', async (req, res) => {
//  const { data, error } = await supabase
//    .from('products')
//    .select('*');
//
//  if (error) return res.status(500).send(error.message);
//  res.json(data);
//});

app.get('/products', async (req, res) => {
  const category = req.query.category;

  let query = supabase.from('products').select('*');

  if (category) {
    query = query.eq('category', category.toLowerCase());
  }

  const { data, error } = await query;

  if (error) return res.status(500).send(error.message);
  res.json(data);
});

app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
});

