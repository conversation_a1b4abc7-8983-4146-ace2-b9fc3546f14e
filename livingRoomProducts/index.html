<!DOCTYPE html>
<!--
  CasaMia Furniture Website - Living Room Products Page

  TECHNOLOGY STACK:
  - Pure HTML5 and CSS3 (No JavaScript dependencies)
  - Responsive design using CSS Grid and Flexbox
  - CSS-only image modals using :target pseudo-selector

  DEVELOPER NOTES:
  - All styling is contained in ../style.css
  - Images are stored in ../Images/livingRoom/ directory
  - Navigation uses .pages-tabs class with .active state management
  - Product grid uses .products-grid class for responsive layout
  - Each product image is clickable for full-screen modal view
  - Product links lead to individual product detail pages

  MAINTENANCE:
  - To add new products: Add figure elements to .products-grid with corresponding modals
  - To modify product images: Update img src attributes and modal images
  - To change navigation: Update nav links and ensure .active class is on current page
  - To add product detail pages: Create new product##.html files and update links
-->
<html lang="en">
<head>
  <!-- Basic HTML5 meta tags for responsive design -->
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CasaMia - Living Room Products</title>
  <!-- Single CSS file contains all styling - no external dependencies -->
  <link rel="stylesheet" href="../style.css">
</head>
<body>
<!--
  NAVIGATION BAR
  - Uses flexbox for horizontal layout
  - .logo class for brand styling
  - .pages-tabs for navigation links
  - .active class indicates current page (Categories)
  - Inline style used for nav container (consider moving to CSS)
-->
<nav style="display: flex; align-items: center; gap: 20px;">
  <p class="logo">CasaMia</p>
  <!-- Navigation links - .active class shows current page -->
  <a class="pages-tabs" href="../home.html">Home</a>
  <a class="pages-tabs" href="../shop.html">Shop</a>
  <a class="pages-tabs active" href="../categories.html">Categories</a>
  <a class="pages-tabs" href="../about.html">About</a>
  <a class="pages-tabs" href="../contact.html">Contact</a>
</nav>
<!--
  PRODUCTS SECTION
  - Main content area displaying living room products
  - .section-header contains back link and page title
  - .products-grid contains product items in responsive grid
  - Each product has clickable image modal and product detail link
-->
<section class="products-section">
  <!-- Section header with back navigation and title -->
  <div class="section-header">
    <a href="../categories.html" class="back-link">← Back to Categories</a>
    <p class="section-title">Living Room Products</p>
  </div>

  <!-- Product grid container -->
  
  <div class="products-grid" id="product-grid">
  <!-- Products will be injected here by JavaScript -->
  </div>
  
  <ul id="product-list"></ul>
</section>



<!--
  FOOTER SECTION
  - Consistent across all pages
  - 4-column layout: Brand info, Quick links, Customer service, Social media
  - Responsive design: stacks on mobile devices
  - Social media links open in new tabs (target="_blank")
  - Copyright notice at bottom
-->
<footer class="footer">
  <!-- Main footer content container -->
  <div class="footer-content">
    <!-- Brand Information Column -->
    <div class="footer-column">
      <h4>CasaMia</h4>
      <p>Transform your space with our curated collection of premium furniture and home décor.</p>
    </div>
    <!-- Quick Navigation Links Column -->
    <div class="footer-column">
      <h4>Quick Links</h4>
      <a href="../home.html">Home</a>
      <a href="../shop.html">Shop</a>
      <a href="../categories.html">Categories</a>
      <a href="../about.html">About</a>
      <a href="../contact.html">Contact</a>
    </div>
    <!-- Customer Service Information Column -->
    <div class="footer-column">
      <h4>Customer Service</h4>
      <p>Shipping Info</p>
      <p>Returns</p>
      <p>Size Guide</p>
      <p>Contact</p>
    </div>
    <!-- Social Media Links Column -->
    <div class="footer-column">
      <h4>Follow Us</h4>
      <div class="footer-social">
        <!-- Social media icons with external links -->
        <a href="https://www.facebook.com/" target="_blank"><img src="../Images/fb.jpg" alt="facebook logo"></a>
        <a href="https://www.twitter.com/" target="_blank"><img src="../Images/x.jpg" alt="twitter logo"></a>
        <a href="https://www.instagram.com/" target="_blank"><img src="../Images/instagram.jpg" alt="instagram logo"></a>
      </div>
    </div>
  </div>
  <!-- Copyright notice -->
  <p class="footer-copyright">© 2025 CasaMia. All rights reserved.</p>
</footer>

<!--
  Add JavaScript to Fetch Products
-->

<script>
  fetch('http://localhost:3001/products?category=living room') // use deployed URL later
    .then(response => response.json())
    .then(products => {
      const grid = document.getElementById('product-grid');

      products.forEach((product, index) => {
        const figure = document.createElement('figure');

        figure.innerHTML = `
          <a href="#modal${index + 1}">
            <img src="${product.image_url}" alt="${product.name}" height="300" width="300">
          </a>
          <figcaption>${product.name}</figcaption>
          <a href="product${String(index + 1).padStart(2, '0')}.html" class="product-link">View Product →</a>
        `;

        grid.appendChild(figure);

        // Create modal (CSS-only style) and inject it into the DOM
        const modal = document.createElement('div');
        modal.id = `modal${index + 1}`;
        modal.className = 'css-modal';
        modal.innerHTML = `
          <div class="css-modal-content">
            <a href="#" class="css-modal-close">&times;</a>
            <img src="${product.image_url}" alt="${product.name}" class="css-modal-image">
          </div>
        `;
        document.body.appendChild(modal);
      });
    })
    .catch(error => {
      console.error('Error loading products:', error);
    });
</script>

</body>
</html>
