{"version": 3, "file": "PostgrestBuilder.d.ts", "sourceRoot": "", "sources": ["../../src/PostgrestBuilder.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EACV,KAAK,EACL,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,kBAAkB,EAClB,qBAAqB,EACtB,MAAM,SAAS,CAAA;AAEhB,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAA;AAE1D,MAAM,CAAC,OAAO,CAAC,QAAQ,OAAO,gBAAgB,CAAC,MAAM,EAAE,YAAY,SAAS,OAAO,GAAG,KAAK,CACzF,YACE,WAAW,CACT,YAAY,SAAS,IAAI,GAAG,wBAAwB,CAAC,MAAM,CAAC,GAAG,uBAAuB,CAAC,MAAM,CAAC,CAC/F;IAEH,SAAS,CAAC,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAA;IAC9D,SAAS,CAAC,GAAG,EAAE,GAAG,CAAA;IAClB,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IACzC,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IACzB,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,CAAA;IACxB,SAAS,CAAC,kBAAkB,UAAQ;IACpC,SAAS,CAAC,MAAM,CAAC,EAAE,WAAW,CAAA;IAC9B,SAAS,CAAC,KAAK,EAAE,KAAK,CAAA;IACtB,SAAS,CAAC,aAAa,EAAE,OAAO,CAAA;gBAEpB,OAAO,EAAE,gBAAgB,CAAC,MAAM,CAAC;IAmB7C;;;;;OAKG;IACH,YAAY,IAAI,IAAI,GAAG,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC;IAKrD;;OAEG;IACH,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IAM5C,IAAI,CACF,QAAQ,GAAG,YAAY,SAAS,IAAI,GAChC,wBAAwB,CAAC,MAAM,CAAC,GAChC,uBAAuB,CAAC,MAAM,CAAC,EACnC,QAAQ,GAAG,KAAK,EAEhB,WAAW,CAAC,EACR,CAAC,CACC,KAAK,EAAE,YAAY,SAAS,IAAI,GAC5B,wBAAwB,CAAC,MAAM,CAAC,GAChC,uBAAuB,CAAC,MAAM,CAAC,KAChC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GACtC,SAAS,GACT,IAAI,EACR,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,GAClF,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAwInC;;;;;OAKG;IACH,OAAO,CAAC,SAAS,KAAK,gBAAgB,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,YAAY,CAAC;IAQhG;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,aAAa,CACX,SAAS,EACT,OAAO,SAAS;QAAE,KAAK,CAAC,EAAE,OAAO,CAAA;KAAE,GAAG;QAAE,KAAK,EAAE,IAAI,CAAA;KAAE,KAClD,gBAAgB,CACnB,qBAAqB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,IAAI,GAE/D,YAAY,CAAC,MAAM,CAAC,SAAS,IAAI,GAC/B,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,GAAG,IAAI,GAClE,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,GAChD,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,EAC9C,YAAY,CACb;CAWF"}