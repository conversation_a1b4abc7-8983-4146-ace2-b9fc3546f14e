{"version": 3, "file": "GoTrueClient.d.ts", "sourceRoot": "", "sources": ["../../src/GoTrueClient.ts"], "names": [], "mappings": "AAAA,OAAO,cAAc,MAAM,kBAAkB,CAAA;AAU7C,OAAO,EACL,SAAS,EAaV,MAAM,cAAc,CAAA;AACrB,OAAO,EACL,KAAK,EAMN,MAAM,aAAa,CAAA;AACpB,OAAO,EACL,QAAQ,EAeT,MAAM,eAAe,CAAA;AAMtB,OAAO,KAAK,EACV,eAAe,EACf,YAAY,EAEZ,iBAAiB,EACjB,yBAAyB,EACzB,eAAe,EACf,sBAAsB,EACtB,mBAAmB,EACnB,gBAAgB,EAChB,aAAa,EACb,WAAW,EAEX,OAAO,EACP,4BAA4B,EAC5B,0BAA0B,EAC1B,6BAA6B,EAC7B,iCAAiC,EACjC,6BAA6B,EAC7B,aAAa,EACb,OAAO,EACP,YAAY,EACZ,gBAAgB,EAChB,IAAI,EACJ,cAAc,EACd,YAAY,EACZ,eAAe,EACf,YAAY,EAcZ,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,4BAA4B,EAK5B,GAAG,EACH,UAAU,EACV,SAAS,EAGT,eAAe,EAChB,MAAM,aAAa,CAAA;AAqBpB,MAAM,CAAC,OAAO,OAAO,YAAY;IAC/B,OAAO,CAAC,MAAM,CAAC,cAAc,CAAI;IAEjC,OAAO,CAAC,UAAU,CAAQ;IAE1B;;;OAGG;IACH,KAAK,EAAE,cAAc,CAAA;IACrB;;OAEG;IACH,GAAG,EAAE,YAAY,CAAA;IACjB;;OAEG;IACH,SAAS,CAAC,UAAU,EAAE,MAAM,CAAA;IAE5B,SAAS,CAAC,QAAQ,EAAE,YAAY,CAAA;IAChC;;OAEG;IACH,SAAS,CAAC,IAAI,EAAE;QAAE,IAAI,EAAE,GAAG,EAAE,CAAA;KAAE,CAAA;IAC/B,SAAS,CAAC,cAAc,EAAE,MAAM,CAAA;IAChC,SAAS,CAAC,gBAAgB,EAAE,OAAO,CAAA;IACnC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAA;IACjC,SAAS,CAAC,OAAO,EAAE,gBAAgB,CAAA;IACnC,SAAS,CAAC,aAAa,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,GAAG,IAAI,CAAO;IAChE,SAAS,CAAC,mBAAmB,EAAE,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAY;IACpE,SAAS,CAAC,iBAAiB,EAAE,UAAU,CAAC,OAAO,WAAW,CAAC,GAAG,IAAI,CAAO;IACzE,SAAS,CAAC,yBAAyB,EAAE,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAO;IACvE,SAAS,CAAC,kBAAkB,EAAE,QAAQ,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAO;IAC5E;;;;;OAKG;IACH,SAAS,CAAC,iBAAiB,EAAE,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAO;IACpE,SAAS,CAAC,kBAAkB,UAAO;IACnC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAA;IACrB,SAAS,CAAC,OAAO,EAAE;QACjB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KACtB,CAAA;IACD,SAAS,CAAC,4BAA4B,UAAQ;IAC9C,SAAS,CAAC,yBAAyB,UAAQ;IAC3C,SAAS,CAAC,KAAK,EAAE,KAAK,CAAA;IACtB,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAA;IACxB,SAAS,CAAC,YAAY,UAAQ;IAC9B,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAK;IAE5C;;OAEG;IACH,SAAS,CAAC,gBAAgB,EAAE,gBAAgB,GAAG,IAAI,CAAO;IAE1D,SAAS,CAAC,gBAAgB,EAAE,OAAO,CAAA;IACnC,SAAS,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAc;IAEzE;;OAEG;gBACS,OAAO,EAAE,mBAAmB;IAyFxC,OAAO,CAAC,MAAM;IAWd;;;;OAIG;IACG,UAAU,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAc7C;;;;;OAKG;YACW,WAAW;IA8EzB;;;;OAIG;IACG,iBAAiB,CAAC,WAAW,CAAC,EAAE,4BAA4B,GAAG,OAAO,CAAC,YAAY,CAAC;IAiC1F;;;;;;;;;OASG;IACG,MAAM,CAAC,WAAW,EAAE,6BAA6B,GAAG,OAAO,CAAC,YAAY,CAAC;IAqE/E;;;;;;;OAOG;IACG,kBAAkB,CACtB,WAAW,EAAE,6BAA6B,GACzC,OAAO,CAAC,yBAAyB,CAAC;IAyDrC;;;OAGG;IACG,eAAe,CAAC,WAAW,EAAE,0BAA0B,GAAG,OAAO,CAAC,aAAa,CAAC;IAStF;;OAEG;IACG,sBAAsB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAQ1E;;;OAGG;IACG,cAAc,CAAC,WAAW,EAAE,eAAe,GAAG,OAAO,CACvD;QACE,IAAI,EAAE;YAAE,OAAO,EAAE,OAAO,CAAC;YAAC,IAAI,EAAE,IAAI,CAAA;SAAE,CAAA;QACtC,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QAAE,IAAI,EAAE;YAAE,OAAO,EAAE,IAAI,CAAC;YAAC,IAAI,EAAE,IAAI,CAAA;SAAE,CAAC;QAAC,KAAK,EAAE,SAAS,CAAA;KAAE,CAC5D;YAUa,gBAAgB;YA4LhB,uBAAuB;IAgDrC;;;OAGG;IACG,iBAAiB,CAAC,WAAW,EAAE,4BAA4B,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAsC9F;;;;;;;;;;;;;;;;OAgBG;IACG,aAAa,CAAC,WAAW,EAAE,iCAAiC,GAAG,OAAO,CAAC,eAAe,CAAC;IAkD7F;;OAEG;IACG,SAAS,CAAC,MAAM,EAAE,eAAe,GAAG,OAAO,CAAC,YAAY,CAAC;IA+C/D;;;;;;;;;;;;;OAaG;IACG,aAAa,CAAC,MAAM,EAAE,aAAa,GAAG,OAAO,CAAC,WAAW,CAAC;IAkChE;;;OAGG;IACG,cAAc,IAAI,OAAO,CAAC,YAAY,CAAC;YAQ/B,eAAe;IAwB7B;;OAEG;IACG,MAAM,CAAC,WAAW,EAAE,YAAY,GAAG,OAAO,CAAC,eAAe,CAAC;IAsCjE;;;;;;;;;;OAUG;IACG,UAAU;;;;;;;;;;;;;;;;IAYhB;;OAEG;YACW,YAAY;IAoE1B;;;;;OAKG;YACW,WAAW;IAmCzB;;;;OAIG;YACW,aAAa;IA+F3B;;;;;;OAMG;IACG,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;YAcpC,QAAQ;IA4CtB;;OAEG;IACG,UAAU,CACd,UAAU,EAAE,cAAc,EAC1B,OAAO,GAAE;QACP,eAAe,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAChC,GACL,OAAO,CAAC,YAAY,CAAC;cAQR,WAAW,CACzB,UAAU,EAAE,cAAc,EAC1B,OAAO,GAAE;QACP,eAAe,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAChC,GACL,OAAO,CAAC,YAAY,CAAC;IA8CxB;;;;OAIG;IACG,UAAU,CAAC,cAAc,EAAE;QAC/B,YAAY,EAAE,MAAM,CAAA;QACpB,aAAa,EAAE,MAAM,CAAA;KACtB,GAAG,OAAO,CAAC,YAAY,CAAC;cAQT,WAAW,CAAC,cAAc,EAAE;QAC1C,YAAY,EAAE,MAAM,CAAA;QACpB,aAAa,EAAE,MAAM,CAAA;KACtB,GAAG,OAAO,CAAC,YAAY,CAAC;IAuDzB;;;;;OAKG;IACG,cAAc,CAAC,cAAc,CAAC,EAAE;QAAE,aAAa,EAAE,MAAM,CAAA;KAAE,GAAG,OAAO,CAAC,YAAY,CAAC;cAQvE,eAAe,CAAC,cAAc,CAAC,EAAE;QAC/C,aAAa,EAAE,MAAM,CAAA;KACtB,GAAG,OAAO,CAAC,YAAY,CAAC;IAoCzB;;OAEG;YACW,kBAAkB;IAmIhC;;OAEG;IACH,OAAO,CAAC,wBAAwB;IAIhC;;OAEG;YACW,eAAe;IAS7B;;;;;;;OAOG;IACG,OAAO,CAAC,OAAO,GAAE,OAA6B,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,SAAS,GAAG,IAAI,CAAA;KAAE,CAAC;cAQ3E,QAAQ,CACtB,EAAE,KAAK,EAAE,GAAE,OAA6B,GACvC,OAAO,CAAC;QAAE,KAAK,EAAE,SAAS,GAAG,IAAI,CAAA;KAAE,CAAC;IA8BvC;;;OAGG;IACH,iBAAiB,CACf,QAAQ,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,GAClF;QACD,IAAI,EAAE;YAAE,YAAY,EAAE,YAAY,CAAA;SAAE,CAAA;KACrC;YA0Ba,mBAAmB;IAmBjC;;;;;;OAMG;IACG,qBAAqB,CACzB,KAAK,EAAE,MAAM,EACb,OAAO,GAAE;QACP,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,YAAY,CAAC,EAAE,MAAM,CAAA;KACjB,GACL,OAAO,CACN;QACE,IAAI,EAAE,EAAE,CAAA;QACR,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,SAAS,CAAA;KAAE,CACnC;IA+BD;;OAEG;IACG,iBAAiB,IAAI,OAAO,CAC9B;QACE,IAAI,EAAE;YACJ,UAAU,EAAE,YAAY,EAAE,CAAA;SAC3B,CAAA;QACD,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,SAAS,CAAA;KAAE,CACnC;IAYD;;;OAGG;IACG,YAAY,CAAC,WAAW,EAAE,0BAA0B,GAAG,OAAO,CAAC,aAAa,CAAC;IAiCnF;;OAEG;IACG,cAAc,CAAC,QAAQ,EAAE,YAAY,GAAG,OAAO,CACjD;QACE,IAAI,EAAE,EAAE,CAAA;QACR,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,SAAS,CAAA;KAAE,CACnC;IAyBD;;;OAGG;YACW,mBAAmB;IA4CjC,OAAO,CAAC,eAAe;YAWT,qBAAqB;IAyBnC;;;OAGG;YACW,kBAAkB;YA0DlB,iBAAiB;YAoDjB,qBAAqB;IAoCnC;;;OAGG;YACW,YAAY;YAQZ,cAAc;IAO5B;;;;;OAKG;IACH,OAAO,CAAC,gCAAgC;IAexC;;;OAGG;YACW,iBAAiB;IAiC/B;;;OAGG;YACW,gBAAgB;IAW9B;;;;;;;;;;;;;;;;;;;;;OAqBG;IACG,gBAAgB;IAKtB;;;;;;;OAOG;IACG,eAAe;IAKrB;;OAEG;YACW,qBAAqB;IAoDnC;;;;OAIG;YACW,uBAAuB;IAyBrC;;OAEG;YACW,oBAAoB;IAwClC;;;;;OAKG;YACW,kBAAkB;YAwClB,SAAS;IAqBvB;;OAEG;YACW,OAAO;IAwCrB;;OAEG;YACW,OAAO;IAwCrB;;OAEG;YACW,UAAU;IA6BxB;;OAEG;YACW,mBAAmB;IAoBjC;;OAEG;YACW,YAAY;IA4B1B;;OAEG;YACW,+BAA+B;YAyC/B,QAAQ;IAkCtB;;;OAGG;IACG,SAAS,CACb,GAAG,CAAC,EAAE,MAAM,EACZ,IAAI,GAAE;QAAE,IAAI,EAAE,GAAG,EAAE,CAAA;KAAiB,GACnC,OAAO,CACN;QACE,IAAI,EAAE;YAAE,MAAM,EAAE,UAAU,CAAC;YAAC,MAAM,EAAE,SAAS,CAAC;YAAC,SAAS,EAAE,UAAU,CAAA;SAAE,CAAA;QACtE,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,SAAS,CAAA;KAAE,GAChC;QAAE,IAAI,EAAE,IAAI,CAAC;QAAC,KAAK,EAAE,IAAI,CAAA;KAAE,CAC9B;CA8EF"}